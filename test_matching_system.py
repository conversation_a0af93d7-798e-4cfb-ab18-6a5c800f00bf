#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易撮合系统测试版本
只处理少量数据来验证基本功能
"""

import pandas as pd
import numpy as np
from trading_matching_system import *

def test_basic_functionality():
    """测试基本功能"""
    print("测试交易撮合系统基本功能")
    print("=" * 50)
    
    # 创建测试订单簿
    order_book = OrderBook("518880", "shl2")
    
    # 测试添加买单
    buy_order = Order(
        order_id="buy_001",
        security_id="518880",
        market="shl2",
        timestamp=93000000,
        price=74.50,
        volume=1000,
        side="B",
        order_type="A"
    )
    
    trades = order_book.add_order(buy_order)
    print(f"添加买单后，成交数量: {len(trades)}")
    
    # 测试添加卖单
    sell_order = Order(
        order_id="sell_001",
        security_id="518880",
        market="shl2",
        timestamp=93000100,
        price=74.40,
        volume=500,
        side="S",
        order_type="A"
    )
    
    trades = order_book.add_order(sell_order)
    print(f"添加卖单后，成交数量: {len(trades)}")
    
    if trades:
        trade = trades[0]
        print(f"成交详情: 价格={trade.price}, 数量={trade.volume}, 方向={trade.bs_flag}")
    
    # 测试市场深度
    bid_prices, bid_volumes, ask_prices, ask_volumes = order_book.get_market_depth()
    print(f"买单深度: {list(zip(bid_prices, bid_volumes))}")
    print(f"卖单深度: {list(zip(ask_prices, ask_volumes))}")
    
    print("\n基本功能测试完成")

def test_data_processing_sample():
    """测试数据处理（样本数据）"""
    print("\n测试数据处理功能")
    print("=" * 50)
    
    try:
        # 读取少量数据进行测试
        order_data = pd.read_csv('data/hq-shl2-518880-4-20250807133643043.csv', nrows=1000)
        trade_data = pd.read_csv('data/hq-shl2-518880-3-20250807134544227.csv', nrows=100)
        tick_data = pd.read_csv('data/hq-shl2-518880-1-20250807165431662.csv', nrows=100)
        
        print(f"读取订单数据: {len(order_data)} 条")
        print(f"读取交易数据: {len(trade_data)} 条")
        print(f"读取tick数据: {len(tick_data)} 条")
        
        # 数据预处理
        order_data['order_price'] = order_data['order_price'].replace(['NULL', 'null', ''], 0)
        order_data['order_price'] = pd.to_numeric(order_data['order_price'], errors='coerce').fillna(0)
        order_data['order_volume'] = order_data['order_volume'].replace(['NULL', 'null', ''], 0)
        order_data['order_volume'] = pd.to_numeric(order_data['order_volume'], errors='coerce').fillna(0)
        
        # 过滤有效数据
        valid_orders = order_data[(order_data['order_price'] > 0) & (order_data['order_volume'] > 0)]
        print(f"有效订单数据: {len(valid_orders)} 条")
        
        if len(valid_orders) > 0:
            print("前5条有效订单:")
            print(valid_orders[['time', 'order_price', 'order_volume', 'order_side', 'order_type']].head())
        
        # 测试交易时段判断
        sample_timestamps = [91500000, 93000000, 113000000, 130000000, 145700000, 150000000]
        for ts in sample_timestamps:
            phase = TradingPhaseManager.get_trading_phase(ts, 'shl2')
            hour = ts // 10000000
            minute = (ts // 100000) % 100
            second = (ts // 1000) % 100
            print(f"时间 {hour:02d}:{minute:02d}:{second:02d} -> 交易阶段: {phase}")
        
    except Exception as e:
        print(f"数据处理测试出错: {e}")
    
    print("数据处理测试完成")

def test_small_dataset():
    """测试小数据集处理"""
    print("\n测试小数据集处理")
    print("=" * 50)
    
    try:
        # 创建处理器
        processor = ShanghaiDataProcessor("518880", "shl2")
        
        # 只读取前1000行数据
        processor.tick_data = pd.read_csv('data/hq-shl2-518880-1-20250807165431662.csv', nrows=100)
        processor.trade_data = pd.read_csv('data/hq-shl2-518880-3-20250807134544227.csv', nrows=100)
        processor.order_data = pd.read_csv('data/hq-shl2-518880-4-20250807133643043.csv', nrows=1000)
        
        # 预处理数据
        processor._preprocess_data()
        
        print(f"预处理后订单数据: {len(processor.order_data)} 条")
        
        # 处理前100个事件
        all_events = []
        
        # 处理order数据
        for _, row in processor.order_data.head(100).iterrows():
            if pd.isna(row.get('biz_index')):
                continue
                
            event = {
                'type': 'order',
                'timestamp': row['timestamp'],
                'biz_index': int(row['biz_index']),
                'data': row
            }
            all_events.append(event)
        
        # 处理trade数据
        for _, row in processor.trade_data.head(50).iterrows():
            if pd.isna(row.get('biz_index')):
                continue
                
            event = {
                'type': 'trade',
                'timestamp': row['timestamp'],
                'biz_index': int(row['biz_index']),
                'data': row
            }
            all_events.append(event)
        
        # 按biz_index排序
        all_events.sort(key=lambda x: x['biz_index'])
        
        print(f"总事件数: {len(all_events)}")
        
        # 处理前50个事件
        processed_count = 0
        for event in all_events[:50]:
            if event['type'] == 'order':
                processor._process_order_event(event['data'])
            elif event['type'] == 'trade':
                processor._process_trade_event(event['data'])
            processed_count += 1
            
            if processed_count % 10 == 0:
                print(f"已处理 {processed_count} 个事件")
        
        print(f"处理完成，生成 {len(processor.trades)} 笔交易，{len(processor.ticks)} 个tick")
        
        # 显示结果摘要
        if processor.trades:
            print("前5笔交易:")
            for i, trade in enumerate(processor.trades[:5]):
                print(f"  {i+1}. 价格={trade.price}, 数量={trade.volume}, 方向={trade.bs_flag}")
        
        if processor.ticks:
            print(f"生成了 {len(processor.ticks)} 个tick")
            
    except Exception as e:
        print(f"小数据集处理出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("小数据集测试完成")

if __name__ == "__main__":
    # 运行测试
    test_basic_functionality()
    test_data_processing_sample()
    test_small_dataset()
    
    print("\n所有测试完成！")
