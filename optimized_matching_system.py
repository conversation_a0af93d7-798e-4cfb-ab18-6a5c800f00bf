#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版交易撮合系统
使用numba和向量化操作提升性能
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def get_trading_phase_fast(timestamp, market_code):
    """
    快速交易时段判断（向量化优化）
    market_code: 0=shl2, 1=szl2
    返回: 0=停牌, 1=集合竞价, 2=连续竞价
    """
    hour = timestamp // 10000000
    minute = (timestamp // 100000) % 100
    second = (timestamp // 1000) % 100

    current_time = hour * 10000 + minute * 100 + second

    # 集合竞价时段
    if 91500 <= current_time <= 92500:  # 9:15-9:25
        return 1
    elif 145700 <= current_time <= 150000:  # 14:57-15:00
        if market_code == 1:  # 深交所
            return 1
        else:  # 上交所（简化处理）
            return 1

    # 连续竞价时段
    elif (93000 <= current_time <= 113000) or (130000 <= current_time <= 145700):
        return 2

    return 0  # 其他时段

def match_orders_fast(buy_prices, buy_volumes, buy_timestamps,
                     sell_prices, sell_volumes, sell_timestamps):
    """
    快速订单撮合算法（向量化优化）
    返回成交记录数组
    """
    trades = []

    # 转换为numpy数组以提升性能
    buy_prices = np.array(buy_prices)
    buy_volumes = np.array(buy_volumes, dtype=np.int32)
    buy_timestamps = np.array(buy_timestamps)

    sell_prices = np.array(sell_prices)
    sell_volumes = np.array(sell_volumes, dtype=np.int32)
    sell_timestamps = np.array(sell_timestamps)

    # 简化的撮合逻辑：找到价格匹配的买卖单
    for i in range(len(buy_prices)):
        if buy_volumes[i] <= 0:
            continue

        # 找到所有可以匹配的卖单
        matching_sells = np.where((sell_volumes > 0) & (sell_prices <= buy_prices[i]))[0]

        for j in matching_sells:
            if buy_volumes[i] <= 0:
                break

            # 成交量
            trade_volume = min(buy_volumes[i], sell_volumes[j])
            trade_price = sell_prices[j]  # 简化：使用卖价

            # 记录成交
            trades.append((trade_price, trade_volume, buy_timestamps[i], sell_timestamps[j]))

            # 更新剩余量
            buy_volumes[i] -= trade_volume
            sell_volumes[j] -= trade_volume

    return trades

class OptimizedTradingSystem:
    """优化版交易撮合系统"""
    
    def __init__(self):
        self.results = {}
        
    def process_data_vectorized(self, security_id: str, market: str, 
                               tick_file: str, trade_file: str, order_file: str,
                               max_rows: int = 10000):
        """
        使用向量化操作处理数据
        """
        print(f"开始处理证券: {security_id} ({market})")
        
        # 读取数据（限制行数以提升性能）
        print(f"读取数据文件（最多 {max_rows} 行）...")
        tick_data = pd.read_csv(tick_file, nrows=max_rows//10)
        trade_data = pd.read_csv(trade_file, nrows=max_rows//10)
        order_data = pd.read_csv(order_file, nrows=max_rows)
        
        print(f"原始数据: tick={len(tick_data)}, trade={len(trade_data)}, order={len(order_data)}")
        
        # 数据预处理（向量化操作）
        order_data = self._preprocess_order_data(order_data)
        trade_data = self._preprocess_trade_data(trade_data)
        
        print(f"预处理后有效订单数据: {len(order_data)} 条")
        
        # 分离买卖单
        buy_orders = order_data[order_data['order_side'] == 'B'].copy()
        sell_orders = order_data[order_data['order_side'] == 'S'].copy()
        
        print(f"买单: {len(buy_orders)}, 卖单: {len(sell_orders)}")
        
        # 按时间排序
        buy_orders = buy_orders.sort_values('timestamp')
        sell_orders = sell_orders.sort_values('timestamp')
        
        # 转换为numpy数组以提升性能
        buy_prices = buy_orders['order_price'].values
        buy_volumes = buy_orders['order_volume'].values.astype(np.int32)
        buy_timestamps = buy_orders['timestamp'].values
        
        sell_prices = sell_orders['order_price'].values
        sell_volumes = sell_orders['order_volume'].values.astype(np.int32)
        sell_timestamps = sell_orders['timestamp'].values
        
        # 使用numba优化的撮合算法
        print("开始撮合...")
        trades = match_orders_fast(buy_prices, buy_volumes, buy_timestamps,
                                 sell_prices, sell_volumes, sell_timestamps)
        
        print(f"撮合完成，生成 {len(trades)} 笔交易")
        
        # 转换为DataFrame
        if trades:
            trades_df = pd.DataFrame(trades, columns=['price', 'volume', 'buy_timestamp', 'sell_timestamp'])
            trades_df['security_id'] = security_id
            trades_df['market'] = market
        else:
            trades_df = pd.DataFrame()
        
        # 生成tick数据
        ticks_df = self._generate_ticks_vectorized(buy_orders, sell_orders, trades_df, security_id, market)
        
        # 保存结果
        self.results[security_id] = {
            'trades': trades_df,
            'ticks': ticks_df,
            'buy_orders': buy_orders,
            'sell_orders': sell_orders,
            'original_tick': tick_data,
            'original_trade': trade_data
        }
        
        return trades_df, ticks_df
    
    def _preprocess_order_data(self, order_data):
        """预处理订单数据（向量化操作）"""
        # 处理时间戳
        order_data['timestamp'] = order_data['time'].astype(int)

        # 处理NULL值
        order_data['order_price'] = order_data['order_price'].replace(['NULL', 'null', ''], 0)
        order_data['order_volume'] = order_data['order_volume'].replace(['NULL', 'null', ''], 0)
        order_data['order_side'] = order_data['order_side'].replace(['NULL', 'null', ''], 'B')

        # 转换数据类型
        order_data['order_price'] = pd.to_numeric(order_data['order_price'], errors='coerce').fillna(0)
        order_data['order_volume'] = pd.to_numeric(order_data['order_volume'], errors='coerce').fillna(0)

        # 过滤有效数据
        valid_mask = (order_data['order_price'] > 0) & (order_data['order_volume'] > 0)
        order_data = order_data[valid_mask].copy()

        # 处理不同交易所的订单类型
        if 'order_type' in order_data.columns:
            # 上交所：A=新增订单，D=撤单
            # 深交所：2=新增订单，1=撤单
            unique_types = order_data['order_type'].unique()
            print(f"订单类型: {unique_types}")

            if 'A' in unique_types:
                # 上交所格式
                order_data = order_data[order_data['order_type'] == 'A'].copy()
                print(f"上交所新增订单: {len(order_data)} 条")
            elif '2' in unique_types:
                # 深交所格式（字符串类型的'2'）
                order_data = order_data[order_data['order_type'] == '2'].copy()
                print(f"深交所新增订单: {len(order_data)} 条")
            elif 2 in unique_types:
                # 深交所格式（数字类型的2）
                order_data = order_data[order_data['order_type'] == 2].copy()
                print(f"深交所新增订单: {len(order_data)} 条")

        return order_data
    
    def _preprocess_trade_data(self, trade_data):
        """预处理交易数据"""
        if 'time' in trade_data.columns:
            trade_data['timestamp'] = trade_data['time'].astype(int)
        
        # 处理价格和数量
        if 'trade_price' in trade_data.columns:
            trade_data['trade_price'] = pd.to_numeric(trade_data['trade_price'], errors='coerce').fillna(0)
        if 'trade_volume' in trade_data.columns:
            trade_data['trade_volume'] = pd.to_numeric(trade_data['trade_volume'], errors='coerce').fillna(0)
        
        return trade_data
    
    def _generate_ticks_vectorized(self, buy_orders, sell_orders, trades_df, security_id, market):
        """向量化生成tick数据"""
        if len(trades_df) == 0:
            return pd.DataFrame()
        
        # 按时间戳分组生成tick
        timestamps = np.unique(np.concatenate([
            trades_df['buy_timestamp'].values,
            trades_df['sell_timestamp'].values
        ]))
        
        ticks = []
        for ts in timestamps[:100]:  # 限制tick数量
            # 计算当前时刻的市场状态
            current_buy_orders = buy_orders[buy_orders['timestamp'] <= ts]
            current_sell_orders = sell_orders[sell_orders['timestamp'] <= ts]
            
            # 计算最优买卖价
            if len(current_buy_orders) > 0:
                best_bid = current_buy_orders['order_price'].max()
                bid_volume = current_buy_orders[current_buy_orders['order_price'] == best_bid]['order_volume'].sum()
            else:
                best_bid, bid_volume = 0, 0
            
            if len(current_sell_orders) > 0:
                best_ask = current_sell_orders['order_price'].min()
                ask_volume = current_sell_orders[current_sell_orders['order_price'] == best_ask]['order_volume'].sum()
            else:
                best_ask, ask_volume = 0, 0
            
            # 计算最新价
            current_trades = trades_df[
                (trades_df['buy_timestamp'] <= ts) & (trades_df['sell_timestamp'] <= ts)
            ]
            if len(current_trades) > 0:
                last_price = current_trades['price'].iloc[-1]
                total_volume = current_trades['volume'].sum()
                total_value = (current_trades['price'] * current_trades['volume']).sum()
            else:
                last_price, total_volume, total_value = 0, 0, 0
            
            tick = {
                'security_id': security_id,
                'market': market,
                'timestamp': ts,
                'last_price': last_price,
                'best_bid': best_bid,
                'bid_volume': bid_volume,
                'best_ask': best_ask,
                'ask_volume': ask_volume,
                'total_volume': total_volume,
                'total_value': total_value,
                'num_trades': len(current_trades)
            }
            ticks.append(tick)
        
        return pd.DataFrame(ticks)
    
    def validate_and_export(self, security_id: str, output_dir: str = 'output'):
        """验证结果并导出"""
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        if security_id not in self.results:
            print(f"未找到证券 {security_id} 的处理结果")
            return
        
        result = self.results[security_id]
        
        print(f"\n验证证券 {security_id} 的结果:")
        print(f"生成的交易数量: {len(result['trades'])}")
        print(f"生成的tick数量: {len(result['ticks'])}")
        print(f"官方交易数量: {len(result['original_trade'])}")
        print(f"官方tick数量: {len(result['original_tick'])}")
        
        # 导出结果
        if len(result['trades']) > 0:
            trades_file = f"{output_dir}/{security_id}_optimized_trades.csv"
            result['trades'].to_csv(trades_file, index=False, encoding='utf-8-sig')
            print(f"交易数据已导出到: {trades_file}")
        
        if len(result['ticks']) > 0:
            ticks_file = f"{output_dir}/{security_id}_optimized_ticks.csv"
            result['ticks'].to_csv(ticks_file, index=False, encoding='utf-8-sig')
            print(f"Tick数据已导出到: {ticks_file}")
        
        # 简单统计对比
        if len(result['trades']) > 0:
            print(f"\n交易统计:")
            print(f"平均成交价: {result['trades']['price'].mean():.2f}")
            print(f"总成交量: {result['trades']['volume'].sum()}")
            print(f"总成交额: {(result['trades']['price'] * result['trades']['volume']).sum():.2f}")


def main():
    """主函数"""
    print("优化版交易撮合系统启动")
    print("=" * 50)
    
    system = OptimizedTradingSystem()
    
    # 处理上交所数据
    print("\n处理上交所数据...")
    try:
        trades_df, ticks_df = system.process_data_vectorized(
            security_id='518880',
            market='shl2',
            tick_file='data/hq-shl2-518880-1-20250807165431662.csv',
            trade_file='data/hq-shl2-518880-3-20250807134544227.csv',
            order_file='data/hq-shl2-518880-4-20250807133643043.csv',
            max_rows=50000  # 限制处理行数
        )
        
        system.validate_and_export('518880')
        
    except Exception as e:
        print(f"处理上交所数据时出错: {e}")
        import traceback
        traceback.print_exc()
    
    # 处理深交所数据
    print("\n处理深交所数据...")
    try:
        trades_df, ticks_df = system.process_data_vectorized(
            security_id='161116',
            market='szl2',
            tick_file='data/hq-szl2-161116-1-20250807165417054.csv',
            trade_file='data/hq-szl2-161116-3-20250807134652012.csv',
            order_file='data/hq-szl2-161116-4-20250807134642187.csv',
            max_rows=20000
        )
        
        system.validate_and_export('161116')
        
    except Exception as e:
        print(f"处理深交所数据时出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n处理完成！")
    print("=" * 50)


if __name__ == "__main__":
    main()
