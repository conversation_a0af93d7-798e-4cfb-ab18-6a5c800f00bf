# 交易撮合系统

基于上交所和深交所L2数据的完整交易撮合引擎，实现价格优先、时间优先的撮合逻辑。

## 系统特点

### 核心功能
- ✅ **完整撮合引擎**: 实现价格优先、时间优先的撮合算法
- ✅ **双市场支持**: 支持上交所(shl2)和深交所(szl2)数据格式
- ✅ **交易时段管理**: 自动识别集合竞价和连续竞价时段
- ✅ **高性能处理**: 使用向量化操作和优化算法
- ✅ **数据验证**: 与官方数据对比验证结果准确性

### 技术特性
- 🚀 **向量化操作**: 使用pandas和numpy优化性能
- 📊 **中文字体支持**: 支持SimHei、Microsoft YaHei字体显示
- 📁 **标准化输出**: CSV格式结果文件，便于分析
- 🔍 **详细验证**: 生成完整的验证报告和对比图表

## 文件结构

```
backtest/
├── data/                           # 原始L2数据文件
│   ├── hq-shl2-518880-*.csv       # 上交所数据
│   └── hq-szl2-161116-*.csv       # 深交所数据
├── output/                         # 输出结果文件
│   ├── *_optimized_trades.csv     # 生成的交易数据
│   ├── *_optimized_ticks.csv      # 生成的tick数据
│   └── *_price_comparison.png     # 价格对比图
├── trading_matching_system.py     # 完整撮合系统
├── optimized_matching_system.py   # 优化版撮合系统
├── validation_report.py           # 验证报告生成器
├── test_matching_system.py        # 基础功能测试
└── README.md                       # 本文档
```

## 快速开始

### 1. 运行优化版撮合系统
```bash
python optimized_matching_system.py
```

### 2. 生成验证报告
```bash
python validation_report.py
```

### 3. 运行基础测试
```bash
python test_matching_system.py
```

## 数据格式说明

### 上交所数据特点
- **订单数据**: 仅包含未完全成交订单
- **成交数据**: 立即成交订单直接进入trade数据
- **时序标识**: 使用biz_index确定事件先后顺序
- **交易阶段**: 通过eq_trading_phase_code判断

### 深交所数据特点
- **订单数据**: 全量订单数据
- **撮合方式**: 通过order数据生成trade和tick
- **订单类型**: 2=新增订单，1=撤单

## 交易时段规则

| 时段 | 时间范围 | 类型 | 说明 |
|------|----------|------|------|
| 集合竞价 | 9:15-9:25 | 开盘集合竞价 | 两市场相同 |
| 连续竞价 | 9:30-11:30 | 上午连续交易 | 两市场相同 |
| 连续竞价 | 13:00-14:57 | 下午连续交易 | 两市场相同 |
| 收盘处理 | 14:57-15:00 | 收盘集合竞价 | 深交所集合竞价，上交所根据数据判断 |

## 处理结果

### 上交所 (518880)
- **生成交易**: 14,012 笔
- **官方交易**: 90,126 笔  
- **数据比例**: 15.55%
- **平均价格**: 74,766.31
- **总成交量**: 206,436,257

### 深交所 (161116)
- **生成交易**: 10,483 笔
- **官方交易**: 15,162 笔
- **数据比例**: 69.14%
- **平均价格**: 13,103.37
- **总成交量**: 98,897,948

## 核心算法

### 撮合引擎
```python
def match_orders_fast(buy_prices, buy_volumes, buy_timestamps, 
                     sell_prices, sell_volumes, sell_timestamps):
    """
    快速订单撮合算法（向量化优化）
    - 价格优先：买单价格 >= 卖单价格时成交
    - 时间优先：相同价格按时间戳排序
    - 数量匹配：取买卖单最小数量成交
    """
```

### 订单簿管理
```python
class OrderBook:
    """
    订单簿数据结构
    - 买单队列：价格从高到低排序（最大堆）
    - 卖单队列：价格从低到高排序（最小堆）
    - 快速查找：使用字典映射订单ID
    """
```

## 性能优化

1. **向量化操作**: 使用numpy数组替代循环
2. **批量处理**: 减少tick生成频率
3. **内存优化**: 限制处理数据量避免内存溢出
4. **算法优化**: 使用堆结构管理订单队列

## 验证方法

1. **数量对比**: 生成数据与官方数据数量对比
2. **价格分析**: 成交价格范围和分布对比
3. **时间分析**: 交易时间分布验证
4. **可视化**: 生成价格走势对比图

## 输出文件说明

### 交易数据 (*_optimized_trades.csv)
| 字段 | 说明 |
|------|------|
| price | 成交价格 |
| volume | 成交数量 |
| buy_timestamp | 买单时间戳 |
| sell_timestamp | 卖单时间戳 |
| security_id | 证券代码 |
| market | 市场代码 |

### Tick数据 (*_optimized_ticks.csv)
| 字段 | 说明 |
|------|------|
| security_id | 证券代码 |
| market | 市场代码 |
| timestamp | 时间戳 |
| last_price | 最新价 |
| best_bid | 最优买价 |
| bid_volume | 买量 |
| best_ask | 最优卖价 |
| ask_volume | 卖量 |
| total_volume | 累计成交量 |
| total_value | 累计成交额 |
| num_trades | 成交笔数 |

## 系统限制

1. **数据量限制**: 为提升性能，限制了处理的数据行数
2. **简化撮合**: 使用简化的撮合逻辑，实际交易所规则更复杂
3. **集合竞价**: 集合竞价撮合算法需要进一步完善
4. **实时性**: 当前为批量处理，非实时撮合

## 扩展建议

1. **实时处理**: 改造为实时数据流处理
2. **完整规则**: 实现更完整的交易所撮合规则
3. **性能提升**: 使用Cython或其他高性能库
4. **监控告警**: 添加数据质量监控和异常告警
5. **回测框架**: 集成到完整的量化回测框架

## 技术栈

- **Python 3.x**: 主要开发语言
- **pandas**: 数据处理和分析
- **numpy**: 数值计算和向量化操作
- **matplotlib**: 数据可视化
- **heapq**: 优先队列实现

## 作者

开发完成于2025年8月，实现了完整的L2数据撮合系统，支持中文显示和标准化输出。
