#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易撮合系统
基于上交所和深交所L2数据实现完整的交易撮合引擎
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass, field
from collections import defaultdict, deque
import heapq
from datetime import datetime, time
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

@dataclass
class Order:
    """订单数据结构"""
    order_id: str
    security_id: str
    market: str
    timestamp: int
    price: float
    volume: int
    side: str  # 'B' for buy, 'S' for sell
    order_type: str  # 'A' for add, 'D' for delete, '1' for cancel (SZ), '2' for add (SZ)
    biz_index: Optional[int] = None
    trading_phase: Optional[str] = None
    
    def __post_init__(self):
        """后处理，确保数据类型正确"""
        self.price = float(self.price) if self.price is not None else 0.0
        self.volume = int(self.volume) if self.volume is not None else 0
        
    def __lt__(self, other):
        """用于优先队列排序：价格优先，时间优先"""
        if self.side == 'B':  # 买单：价格高的优先，时间早的优先
            if self.price != other.price:
                return self.price > other.price
            return self.timestamp < other.timestamp
        else:  # 卖单：价格低的优先，时间早的优先
            if self.price != other.price:
                return self.price < other.price
            return self.timestamp < other.timestamp

@dataclass
class Trade:
    """成交数据结构"""
    trade_id: str
    security_id: str
    market: str
    timestamp: int
    price: float
    volume: int
    bs_flag: str  # 'B' for buy-initiated, 'S' for sell-initiated
    buy_order_id: Optional[str] = None
    sell_order_id: Optional[str] = None
    biz_index: Optional[int] = None

@dataclass
class Tick:
    """tick数据结构"""
    security_id: str
    market: str
    timestamp: int
    trading_phase: str
    last_price: float = 0.0
    bid_prices: List[float] = field(default_factory=list)
    bid_volumes: List[int] = field(default_factory=list)
    ask_prices: List[float] = field(default_factory=list)
    ask_volumes: List[int] = field(default_factory=list)
    total_volume: int = 0
    total_value: float = 0.0
    num_trades: int = 0

class TradingPhaseManager:
    """交易时段管理器"""
    
    @staticmethod
    def get_trading_phase(timestamp: int, market: str, eq_trading_phase_code: str = None) -> str:
        """
        根据时间戳和市场确定交易阶段
        
        Args:
            timestamp: 时间戳 (HHMMSSMMM格式)
            market: 市场代码 ('shl2' or 'szl2')
            eq_trading_phase_code: 上交所的交易阶段代码
            
        Returns:
            'C' for 集合竞价, 'T' for 连续竞价
        """
        hour = timestamp // 10000000
        minute = (timestamp // 100000) % 100
        second = (timestamp // 1000) % 100
        
        current_time = hour * 10000 + minute * 100 + second
        
        # 集合竞价时段
        if 91500 <= current_time <= 92500:  # 9:15-9:25
            return 'C'
        elif 145700 <= current_time <= 150000:  # 14:57-15:00
            if market == 'szl2':
                return 'C'  # 深交所集合竞价
            else:
                # 上交所根据eq_trading_phase_code判断
                if eq_trading_phase_code and 'C' in eq_trading_phase_code:
                    return 'C'
                else:
                    return 'T'
        
        # 连续竞价时段
        elif (93000 <= current_time <= 113000) or (130000 <= current_time <= 145700):
            return 'T'
        
        # 其他时段
        return 'S'  # 停牌或其他状态

class OrderBook:
    """订单簿"""
    
    def __init__(self, security_id: str, market: str):
        self.security_id = security_id
        self.market = market
        self.buy_orders = []  # 买单优先队列（最大堆）
        self.sell_orders = []  # 卖单优先队列（最小堆）
        self.order_dict = {}  # 订单ID到订单的映射
        self.last_price = 0.0
        self.total_volume = 0
        self.total_value = 0.0
        self.num_trades = 0
        
    def add_order(self, order: Order) -> List[Trade]:
        """
        添加订单到订单簿
        
        Returns:
            生成的成交记录列表
        """
        trades = []
        
        if order.side == 'B':  # 买单
            # 尝试与卖单撮合
            while (self.sell_orders and 
                   order.volume > 0 and 
                   self.sell_orders[0].price <= order.price):
                
                sell_order = heapq.heappop(self.sell_orders)
                trade = self._execute_trade(order, sell_order)
                if trade:
                    trades.append(trade)
                    
                # 如果卖单还有剩余，重新加入队列
                if sell_order.volume > 0:
                    heapq.heappush(self.sell_orders, sell_order)
                else:
                    # 完全成交，从字典中删除
                    self.order_dict.pop(sell_order.order_id, None)
            
            # 如果买单还有剩余，加入买单队列
            if order.volume > 0:
                heapq.heappush(self.buy_orders, order)
                self.order_dict[order.order_id] = order
                
        else:  # 卖单
            # 尝试与买单撮合
            while (self.buy_orders and 
                   order.volume > 0 and 
                   self.buy_orders[0].price >= order.price):
                
                buy_order = heapq.heappop(self.buy_orders)
                trade = self._execute_trade(buy_order, order)
                if trade:
                    trades.append(trade)
                    
                # 如果买单还有剩余，重新加入队列
                if buy_order.volume > 0:
                    heapq.heappush(self.buy_orders, buy_order)
                else:
                    # 完全成交，从字典中删除
                    self.order_dict.pop(buy_order.order_id, None)
            
            # 如果卖单还有剩余，加入卖单队列
            if order.volume > 0:
                heapq.heappush(self.sell_orders, order)
                self.order_dict[order.order_id] = order
        
        return trades
    
    def cancel_order(self, order_id: str) -> bool:
        """撤销订单"""
        if order_id not in self.order_dict:
            return False
            
        order = self.order_dict[order_id]
        order.volume = 0  # 标记为已撤销
        del self.order_dict[order_id]
        return True
    
    def _execute_trade(self, buy_order: Order, sell_order: Order) -> Optional[Trade]:
        """执行交易"""
        if buy_order.volume <= 0 or sell_order.volume <= 0:
            return None
            
        # 成交量为两个订单的最小值
        trade_volume = min(buy_order.volume, sell_order.volume)
        
        # 成交价格（这里简化为卖单价格，实际可能更复杂）
        trade_price = sell_order.price
        
        # 更新订单剩余量
        buy_order.volume -= trade_volume
        sell_order.volume -= trade_volume
        
        # 更新统计信息
        self.last_price = trade_price
        self.total_volume += trade_volume
        self.total_value += trade_price * trade_volume
        self.num_trades += 1
        
        # 创建成交记录
        trade = Trade(
            trade_id=f"{self.num_trades}",
            security_id=self.security_id,
            market=self.market,
            timestamp=max(buy_order.timestamp, sell_order.timestamp),
            price=trade_price,
            volume=trade_volume,
            bs_flag='B' if buy_order.timestamp >= sell_order.timestamp else 'S',
            buy_order_id=buy_order.order_id,
            sell_order_id=sell_order.order_id
        )
        
        return trade
    
    def get_market_depth(self, levels: int = 5) -> Tuple[List[float], List[int], List[float], List[int]]:
        """获取市场深度（优化版本）"""
        buy_prices, buy_volumes = [], []
        sell_prices, sell_volumes = [], []

        # 优化：只处理有限数量的订单，避免性能问题
        max_orders_to_process = 1000

        # 获取买单深度（价格从高到低）
        if self.buy_orders:
            # 使用字典聚合相同价格的订单
            buy_price_volume_map = defaultdict(int)
            processed_count = 0

            for order in self.buy_orders:
                if order.volume > 0 and processed_count < max_orders_to_process:
                    buy_price_volume_map[order.price] += order.volume
                    processed_count += 1

            # 按价格排序（从高到低）
            sorted_buy_prices = sorted(buy_price_volume_map.keys(), reverse=True)[:levels]
            for price in sorted_buy_prices:
                buy_prices.append(price)
                buy_volumes.append(buy_price_volume_map[price])

        # 获取卖单深度（价格从低到高）
        if self.sell_orders:
            sell_price_volume_map = defaultdict(int)
            processed_count = 0

            for order in self.sell_orders:
                if order.volume > 0 and processed_count < max_orders_to_process:
                    sell_price_volume_map[order.price] += order.volume
                    processed_count += 1

            # 按价格排序（从低到高）
            sorted_sell_prices = sorted(sell_price_volume_map.keys())[:levels]
            for price in sorted_sell_prices:
                sell_prices.append(price)
                sell_volumes.append(sell_price_volume_map[price])

        return buy_prices, buy_volumes, sell_prices, sell_volumes


class DataProcessor:
    """数据处理器基类"""

    def __init__(self, security_id: str, market: str):
        self.security_id = security_id
        self.market = market
        self.order_book = OrderBook(security_id, market)
        self.trades = []
        self.ticks = []

    def load_data(self, tick_file: str, trade_file: str, order_file: str):
        """加载数据文件"""
        self.tick_data = pd.read_csv(tick_file)
        self.trade_data = pd.read_csv(trade_file)
        self.order_data = pd.read_csv(order_file)

        # 数据预处理
        self._preprocess_data()

    def _preprocess_data(self):
        """数据预处理"""
        # 处理时间戳
        for df in [self.tick_data, self.trade_data, self.order_data]:
            if 'time' in df.columns:
                df['timestamp'] = df['time'].astype(int)

        # 处理价格和数量字段，将NULL和空值转换为0
        if 'order_price' in self.order_data.columns:
            self.order_data['order_price'] = self.order_data['order_price'].replace(['NULL', 'null', ''], 0)
            self.order_data['order_price'] = pd.to_numeric(self.order_data['order_price'], errors='coerce').fillna(0)
        if 'order_volume' in self.order_data.columns:
            self.order_data['order_volume'] = self.order_data['order_volume'].replace(['NULL', 'null', ''], 0)
            self.order_data['order_volume'] = pd.to_numeric(self.order_data['order_volume'], errors='coerce').fillna(0)
        if 'order_side' in self.order_data.columns:
            self.order_data['order_side'] = self.order_data['order_side'].replace(['NULL', 'null', ''], 'B')
        if 'order_type' in self.order_data.columns:
            self.order_data['order_type'] = self.order_data['order_type'].replace(['NULL', 'null', ''], 'A')

        # 过滤掉无效的订单数据（价格或数量为0或NULL的）
        if 'order_price' in self.order_data.columns and 'order_volume' in self.order_data.columns:
            valid_mask = (self.order_data['order_price'] > 0) & (self.order_data['order_volume'] > 0)
            self.order_data = self.order_data[valid_mask].copy()
            print(f"过滤后剩余有效订单数据: {len(self.order_data)} 条")

    def process(self):
        """处理数据，生成撮合结果"""
        raise NotImplementedError("子类必须实现此方法")


class ShanghaiDataProcessor(DataProcessor):
    """上交所数据处理器"""

    def process(self):
        """
        处理上交所数据
        上交所特点：order数据仅包含未完全成交订单，立即成交订单直接进入trade数据
        """
        print(f"开始处理上交所数据: {self.security_id}")

        # 合并order和trade数据，按biz_index排序
        all_events = []

        # 处理order数据
        for _, row in self.order_data.iterrows():
            if pd.isna(row.get('biz_index')):
                continue

            event = {
                'type': 'order',
                'timestamp': row['timestamp'],
                'biz_index': int(row['biz_index']),
                'data': row
            }
            all_events.append(event)

        # 处理trade数据
        for _, row in self.trade_data.iterrows():
            if pd.isna(row.get('biz_index')):
                continue

            event = {
                'type': 'trade',
                'timestamp': row['timestamp'],
                'biz_index': int(row['biz_index']),
                'data': row
            }
            all_events.append(event)

        # 按biz_index排序
        all_events.sort(key=lambda x: x['biz_index'])

        # 逐个处理事件
        for event in all_events:
            if event['type'] == 'order':
                self._process_order_event(event['data'])
            elif event['type'] == 'trade':
                self._process_trade_event(event['data'])

        print(f"处理完成，生成 {len(self.trades)} 笔交易，{len(self.ticks)} 个tick")

    def _process_order_event(self, row):
        """处理订单事件"""
        try:
            # 跳过无效的订单数据
            if (pd.isna(row.get('order_price')) or
                pd.isna(row.get('order_volume')) or
                row.get('order_price', 0) <= 0 or
                row.get('order_volume', 0) <= 0):
                return

            order_id = str(row.get('order_origin_no', row.get('order_index', '')))
            if not order_id or order_id == 'nan' or order_id == 'NULL':
                return

            # 获取交易阶段
            trading_phase = TradingPhaseManager.get_trading_phase(
                row['timestamp'],
                self.market,
                row.get('eq_trading_phase_code', '')
            )

            if row['order_type'] == 'A':  # 新增订单
                order = Order(
                    order_id=order_id,
                    security_id=self.security_id,
                    market=self.market,
                    timestamp=row['timestamp'],
                    price=float(row.get('order_price', 0)),
                    volume=int(row.get('order_volume', 0)),
                    side=row.get('order_side', 'B'),
                    order_type=row['order_type'],
                    biz_index=int(row.get('biz_index', 0)),
                    trading_phase=trading_phase
                )

                if trading_phase == 'T':  # 连续竞价
                    trades = self.order_book.add_order(order)
                    self.trades.extend(trades)

                    # 每100个订单生成一次tick，减少计算频率
                    if len(self.trades) % 100 == 0 or len(self.order_book.order_dict) % 100 == 0:
                        self._generate_tick(row['timestamp'], trading_phase)

            elif row['order_type'] == 'D':  # 撤单
                self.order_book.cancel_order(order_id)
                # 撤单时也减少tick生成频率
                if len(self.order_book.order_dict) % 100 == 0:
                    self._generate_tick(row['timestamp'], trading_phase)

        except Exception as e:
            print(f"处理订单事件出错: {e}, 订单ID: {row.get('order_index', 'N/A')}")

    def _process_trade_event(self, row):
        """处理成交事件"""
        try:
            trade = Trade(
                trade_id=str(row.get('trade_index', '')),
                security_id=self.security_id,
                market=self.market,
                timestamp=row['timestamp'],
                price=float(row.get('trade_price', 0)),
                volume=int(row.get('trade_volume', 0)),
                bs_flag=row.get('trade_bs_flag', 'B'),
                buy_order_id=str(row.get('trade_buy_no', '')),
                sell_order_id=str(row.get('trade_sell_no', '')),
                biz_index=int(row.get('biz_index', 0))
            )

            self.trades.append(trade)

            # 更新订单簿统计信息
            self.order_book.last_price = trade.price
            self.order_book.total_volume += trade.volume
            self.order_book.total_value += trade.price * trade.volume
            self.order_book.num_trades += 1

            # 获取交易阶段
            trading_phase = TradingPhaseManager.get_trading_phase(
                row['timestamp'],
                self.market
            )

            # 生成tick
            self._generate_tick(row['timestamp'], trading_phase)

        except Exception as e:
            print(f"处理成交事件出错: {e}, 数据: {row}")

    def _generate_tick(self, timestamp: int, trading_phase: str):
        """生成tick数据"""
        bid_prices, bid_volumes, ask_prices, ask_volumes = self.order_book.get_market_depth()

        tick = Tick(
            security_id=self.security_id,
            market=self.market,
            timestamp=timestamp,
            trading_phase=trading_phase,
            last_price=self.order_book.last_price,
            bid_prices=bid_prices,
            bid_volumes=bid_volumes,
            ask_prices=ask_prices,
            ask_volumes=ask_volumes,
            total_volume=self.order_book.total_volume,
            total_value=self.order_book.total_value,
            num_trades=self.order_book.num_trades
        )

        self.ticks.append(tick)


class ShenzhenDataProcessor(DataProcessor):
    """深交所数据处理器"""

    def process(self):
        """
        处理深交所数据
        深交所特点：order数据为全量订单，可通过撮合order数据生成trade和tick数据
        """
        print(f"开始处理深交所数据: {self.security_id}")

        # 按时间戳排序order数据
        self.order_data = self.order_data.sort_values('timestamp')

        # 逐个处理订单
        for _, row in self.order_data.iterrows():
            self._process_order_event(row)

        print(f"处理完成，生成 {len(self.trades)} 笔交易，{len(self.ticks)} 个tick")

    def _process_order_event(self, row):
        """处理订单事件"""
        try:
            order_id = str(row.get('order_index', ''))
            if not order_id or order_id == 'nan':
                return

            # 获取交易阶段
            trading_phase = TradingPhaseManager.get_trading_phase(
                row['timestamp'],
                self.market
            )

            if row['order_type'] == '2':  # 下单
                # 检查是否为撤单（价格为0）
                if float(row.get('order_price', 0)) == 0:
                    return

                order = Order(
                    order_id=order_id,
                    security_id=self.security_id,
                    market=self.market,
                    timestamp=row['timestamp'],
                    price=float(row.get('order_price', 0)),
                    volume=int(row.get('order_volume', 0)),
                    side=row.get('order_side', 'B'),
                    order_type=row['order_type'],
                    trading_phase=trading_phase
                )

                if trading_phase == 'T':  # 连续竞价
                    trades = self.order_book.add_order(order)
                    self.trades.extend(trades)

                    # 生成tick
                    self._generate_tick(row['timestamp'], trading_phase)
                elif trading_phase == 'C':  # 集合竞价
                    # 集合竞价期间只添加订单，不撮合
                    if order.side == 'B':
                        heapq.heappush(self.order_book.buy_orders, order)
                    else:
                        heapq.heappush(self.order_book.sell_orders, order)
                    self.order_book.order_dict[order_id] = order

            elif row['order_type'] == '1':  # 撤单
                self.order_book.cancel_order(order_id)
                if trading_phase == 'T':
                    self._generate_tick(row['timestamp'], trading_phase)

        except Exception as e:
            print(f"处理订单事件出错: {e}, 数据: {row}")

    def _generate_tick(self, timestamp: int, trading_phase: str):
        """生成tick数据"""
        bid_prices, bid_volumes, ask_prices, ask_volumes = self.order_book.get_market_depth()

        tick = Tick(
            security_id=self.security_id,
            market=self.market,
            timestamp=timestamp,
            trading_phase=trading_phase,
            last_price=self.order_book.last_price,
            bid_prices=bid_prices,
            bid_volumes=bid_volumes,
            ask_prices=ask_prices,
            ask_volumes=ask_volumes,
            total_volume=self.order_book.total_volume,
            total_value=self.order_book.total_value,
            num_trades=self.order_book.num_trades
        )

        self.ticks.append(tick)

    def call_auction_matching(self, timestamp: int):
        """集合竞价撮合"""
        if not self.order_book.buy_orders or not self.order_book.sell_orders:
            return

        # 找到最优成交价格
        auction_price = self._find_auction_price()
        if auction_price is None:
            return

        # 按成交价格撮合
        buy_orders_to_match = [o for o in self.order_book.buy_orders if o.price >= auction_price]
        sell_orders_to_match = [o for o in self.order_book.sell_orders if o.price <= auction_price]

        # 按时间优先排序
        buy_orders_to_match.sort(key=lambda x: x.timestamp)
        sell_orders_to_match.sort(key=lambda x: x.timestamp)

        # 撮合成交
        i, j = 0, 0
        while i < len(buy_orders_to_match) and j < len(sell_orders_to_match):
            buy_order = buy_orders_to_match[i]
            sell_order = sell_orders_to_match[j]

            if buy_order.volume <= 0:
                i += 1
                continue
            if sell_order.volume <= 0:
                j += 1
                continue

            # 执行成交
            trade_volume = min(buy_order.volume, sell_order.volume)

            trade = Trade(
                trade_id=f"auction_{len(self.trades)}",
                security_id=self.security_id,
                market=self.market,
                timestamp=timestamp,
                price=auction_price,
                volume=trade_volume,
                bs_flag='C',  # 集合竞价成交
                buy_order_id=buy_order.order_id,
                sell_order_id=sell_order.order_id
            )

            self.trades.append(trade)

            # 更新订单剩余量
            buy_order.volume -= trade_volume
            sell_order.volume -= trade_volume

            # 更新统计信息
            self.order_book.last_price = auction_price
            self.order_book.total_volume += trade_volume
            self.order_book.total_value += auction_price * trade_volume
            self.order_book.num_trades += 1

            if buy_order.volume == 0:
                i += 1
            if sell_order.volume == 0:
                j += 1

        # 清理已完全成交的订单
        self.order_book.buy_orders = [o for o in self.order_book.buy_orders if o.volume > 0]
        self.order_book.sell_orders = [o for o in self.order_book.sell_orders if o.volume > 0]
        heapq.heapify(self.order_book.buy_orders)
        heapq.heapify(self.order_book.sell_orders)

        # 生成集合竞价结束后的tick
        self._generate_tick(timestamp, 'C')

    def _find_auction_price(self) -> Optional[float]:
        """找到集合竞价的最优成交价格"""
        if not self.order_book.buy_orders or not self.order_book.sell_orders:
            return None

        # 获取所有可能的成交价格
        all_prices = set()
        for order in self.order_book.buy_orders + self.order_book.sell_orders:
            all_prices.add(order.price)

        max_volume = 0
        best_price = None

        for price in all_prices:
            # 计算在此价格下的可成交量
            buy_volume = sum(o.volume for o in self.order_book.buy_orders if o.price >= price)
            sell_volume = sum(o.volume for o in self.order_book.sell_orders if o.price <= price)

            match_volume = min(buy_volume, sell_volume)

            if match_volume > max_volume:
                max_volume = match_volume
                best_price = price

        return best_price if max_volume > 0 else None


class TradingMatchingSystem:
    """交易撮合系统主类"""

    def __init__(self):
        self.processors = {}
        self.results = {}

    def process_security(self, security_id: str, market: str,
                        tick_file: str, trade_file: str, order_file: str):
        """处理单个证券的数据"""
        print(f"\n开始处理证券: {security_id} ({market})")

        # 选择合适的处理器
        if market == 'shl2':
            processor = ShanghaiDataProcessor(security_id, market)
        elif market == 'szl2':
            processor = ShenzhenDataProcessor(security_id, market)
        else:
            raise ValueError(f"不支持的市场: {market}")

        # 加载和处理数据
        processor.load_data(tick_file, trade_file, order_file)
        processor.process()

        # 保存处理器和结果
        self.processors[security_id] = processor
        self.results[security_id] = {
            'trades': processor.trades,
            'ticks': processor.ticks,
            'order_book': processor.order_book
        }

        return processor

    def validate_results(self, security_id: str):
        """验证结果与官方数据的一致性"""
        if security_id not in self.processors:
            print(f"未找到证券 {security_id} 的处理结果")
            return

        processor = self.processors[security_id]

        print(f"\n验证证券 {security_id} 的结果:")
        print(f"生成的交易数量: {len(processor.trades)}")
        print(f"生成的tick数量: {len(processor.ticks)}")

        # 与官方tick数据对比
        official_ticks = processor.tick_data
        generated_ticks = self._convert_ticks_to_dataframe(processor.ticks)

        print(f"官方tick数量: {len(official_ticks)}")

        # 简单的数量对比
        if len(generated_ticks) > 0 and len(official_ticks) > 0:
            print(f"tick数量比例: {len(generated_ticks) / len(official_ticks):.2%}")

        return generated_ticks, official_ticks

    def _convert_ticks_to_dataframe(self, ticks: List[Tick]) -> pd.DataFrame:
        """将tick对象列表转换为DataFrame"""
        if not ticks:
            return pd.DataFrame()

        data = []
        for tick in ticks:
            row = {
                'securityid': tick.security_id,
                'market': tick.market,
                'timestamp': tick.timestamp,
                'trading_phase': tick.trading_phase,
                'last_price': tick.last_price,
                'total_volume': tick.total_volume,
                'total_value': tick.total_value,
                'num_trades': tick.num_trades,
                'bid_prices': tick.bid_prices,
                'bid_volumes': tick.bid_volumes,
                'ask_prices': tick.ask_prices,
                'ask_volumes': tick.ask_volumes
            }
            data.append(row)

        return pd.DataFrame(data)

    def export_results(self, security_id: str, output_dir: str = 'output'):
        """导出结果到CSV文件"""
        import os
        os.makedirs(output_dir, exist_ok=True)

        if security_id not in self.processors:
            print(f"未找到证券 {security_id} 的处理结果")
            return

        processor = self.processors[security_id]

        # 导出交易数据
        if processor.trades:
            trades_data = []
            for trade in processor.trades:
                trades_data.append({
                    'trade_id': trade.trade_id,
                    'security_id': trade.security_id,
                    'market': trade.market,
                    'timestamp': trade.timestamp,
                    'price': trade.price,
                    'volume': trade.volume,
                    'bs_flag': trade.bs_flag,
                    'buy_order_id': trade.buy_order_id,
                    'sell_order_id': trade.sell_order_id
                })

            trades_df = pd.DataFrame(trades_data)
            trades_file = f"{output_dir}/{security_id}_generated_trades.csv"
            trades_df.to_csv(trades_file, index=False, encoding='utf-8-sig')
            print(f"交易数据已导出到: {trades_file}")

        # 导出tick数据
        if processor.ticks:
            ticks_df = self._convert_ticks_to_dataframe(processor.ticks)
            ticks_file = f"{output_dir}/{security_id}_generated_ticks.csv"
            ticks_df.to_csv(ticks_file, index=False, encoding='utf-8-sig')
            print(f"Tick数据已导出到: {ticks_file}")

        # 导出订单簿状态
        bid_prices, bid_volumes, ask_prices, ask_volumes = processor.order_book.get_market_depth(10)
        orderbook_data = {
            'security_id': security_id,
            'market': processor.market,
            'last_price': processor.order_book.last_price,
            'total_volume': processor.order_book.total_volume,
            'total_value': processor.order_book.total_value,
            'num_trades': processor.order_book.num_trades,
            'bid_prices': bid_prices,
            'bid_volumes': bid_volumes,
            'ask_prices': ask_prices,
            'ask_volumes': ask_volumes
        }

        orderbook_df = pd.DataFrame([orderbook_data])
        orderbook_file = f"{output_dir}/{security_id}_final_orderbook.csv"
        orderbook_df.to_csv(orderbook_file, index=False, encoding='utf-8-sig')
        print(f"最终订单簿状态已导出到: {orderbook_file}")


def main():
    """主函数"""
    print("交易撮合系统启动")
    print("=" * 50)

    # 创建撮合系统
    system = TradingMatchingSystem()

    # 处理上交所数据
    print("\n处理上交所数据...")
    try:
        sh_processor = system.process_security(
            security_id='518880',
            market='shl2',
            tick_file='data/hq-shl2-518880-1-20250807165431662.csv',
            trade_file='data/hq-shl2-518880-3-20250807134544227.csv',
            order_file='data/hq-shl2-518880-4-20250807133643043.csv'
        )

        # 验证结果
        system.validate_results('518880')

        # 导出结果
        system.export_results('518880')

    except Exception as e:
        print(f"处理上交所数据时出错: {e}")

    # 处理深交所数据
    print("\n处理深交所数据...")
    try:
        sz_processor = system.process_security(
            security_id='161116',
            market='szl2',
            tick_file='data/hq-szl2-161116-1-20250807165417054.csv',
            trade_file='data/hq-szl2-161116-3-20250807134652012.csv',
            order_file='data/hq-szl2-161116-4-20250807134642187.csv'
        )

        # 验证结果
        system.validate_results('161116')

        # 导出结果
        system.export_results('161116')

    except Exception as e:
        print(f"处理深交所数据时出错: {e}")

    print("\n处理完成！")
    print("=" * 50)


if __name__ == "__main__":
    main()
